namespace FleetXQ.Tools.BulkImporter.Core.Logging;

/// <summary>
/// Provides correlation context for tracking operations across the application
/// </summary>
public static class CorrelationContext
{
    private static readonly AsyncLocal<string?> _correlationId = new();
    private static readonly AsyncLocal<string?> _operationId = new();

    /// <summary>
    /// Gets or sets the correlation ID for the current async context
    /// </summary>
    public static string? CorrelationId
    {
        get => _correlationId.Value;
        set => _correlationId.Value = value;
    }

    /// <summary>
    /// Gets or sets the operation ID for the current async context
    /// </summary>
    public static string? OperationId
    {
        get => _operationId.Value;
        set => _operationId.Value = value;
    }

    /// <summary>
    /// Creates a new correlation ID if one doesn't exist
    /// </summary>
    public static string EnsureCorrelationId()
    {
        if (string.IsNullOrEmpty(CorrelationId))
        {
            CorrelationId = Guid.NewGuid().ToString("N")[..8];
        }
        return CorrelationId;
    }

    /// <summary>
    /// Creates a new operation ID
    /// </summary>
    public static string CreateOperationId(string operation)
    {
        OperationId = $"{operation}_{Guid.NewGuid().ToString("N")[..6]}";
        return OperationId;
    }

    /// <summary>
    /// Clears the correlation context
    /// </summary>
    public static void Clear()
    {
        CorrelationId = null;
        OperationId = null;
    }
}
