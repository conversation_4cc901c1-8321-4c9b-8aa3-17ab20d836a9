// Import Bootstrap with custom variables
@import "variables";

// Custom styles for FleetXQ Bulk Importer
body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
}

// Compact design utilities
.compact {
  padding: $compact-padding-y $compact-padding-x;
  margin: $compact-margin;
}

// Touch-friendly elements
.touch-target {
  min-height: $touch-target-size;
  min-width: $touch-target-size;
  padding: $touch-target-spacing;
}

// Custom card styles
.card {
  border: $card-border-width;
  box-shadow: $card-box-shadow;
  border-radius: $card-border-radius;
  
  .card-header {
    background-color: $light;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  }
}

// Progress indicators
.progress-container {
  .progress {
    height: $progress-height;
    border-radius: $progress-border-radius;
    
    .progress-bar {
      transition: width 0.3s ease;
    }
  }
  
  .progress-text {
    font-size: 0.875rem;
    color: $dark;
    margin-top: 0.25rem;
  }
}

// Form enhancements
.form-control {
  border-radius: $input-border-radius;
  
  &:focus {
    border-color: $input-focus-border-color;
    box-shadow: $input-focus-box-shadow;
  }
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

// Button enhancements
.btn {
  border-radius: $btn-border-radius;
  font-weight: $btn-font-weight;
  
  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

// Wizard-specific styles
.wizard-container {
  .wizard-step {
    padding: 2rem;
    
    &.active {
      background-color: $light;
      border-radius: $border-radius;
    }
  }
  
  .wizard-navigation {
    padding: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    
    .btn {
      min-width: 100px;
    }
  }
}

// Status indicators
.status-indicator {
  display: inline-flex;
  align-items: center;
  
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
  }
  
  &.status-success::before {
    background-color: $success;
  }
  
  &.status-warning::before {
    background-color: $warning;
  }
  
  &.status-danger::before {
    background-color: $danger;
  }
  
  &.status-info::before {
    background-color: $info;
  }
}

// Loading states
.loading {
  opacity: 0.6;
  pointer-events: none;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid $primary;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive utilities
@media (max-width: 768px) {
  .wizard-container .wizard-step {
    padding: 1rem;
  }
  
  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .card-body {
    padding: 1rem;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .card {
    border: 1px solid $dark;
  }
  
  .btn {
    border-width: 2px;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .progress-bar,
  .loading::after {
    transition: none;
    animation: none;
  }
}
