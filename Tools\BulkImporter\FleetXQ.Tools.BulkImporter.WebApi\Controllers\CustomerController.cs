using Microsoft.AspNetCore.Mvc;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Controller for customer management operations in the bulk importer
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class CustomerController : ControllerBase
{
    private readonly ILogger<CustomerController> _logger;

    public CustomerController(ILogger<CustomerController> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Gets customers for a specific dealer
    /// </summary>
    /// <param name="dealerId">Dealer ID to get customers for</param>
    /// <param name="activeOnly">Whether to return only active customers (default: true)</param>
    /// <param name="pageNumber">Page number for pagination (default: 1)</param>
    /// <param name="pageSize">Page size for pagination (default: 50, max: 100)</param>
    /// <returns>List of customers for the dealer</returns>
    [HttpGet]
    public ActionResult<CustomerListResponse> GetCustomers(
        [FromQuery] Guid dealerId,
        [FromQuery] bool activeOnly = true,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            if (dealerId == Guid.Empty)
            {
                return BadRequest("Dealer ID cannot be empty");
            }

            _logger.LogDebug("Getting customers for dealer: {DealerId}", dealerId);

            // Mock data for demonstration
            var customers = GetMockCustomers(dealerId, activeOnly);
            var totalCount = customers.Count;
            var pagedCustomers = customers
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var response = new CustomerListResponse
            {
                Customers = pagedCustomers,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                DealerId = dealerId
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customers for dealer: {DealerId}", dealerId);
            return Problem("An error occurred while retrieving customers");
        }
    }

    /// <summary>
    /// Gets a specific customer by ID
    /// </summary>
    /// <param name="id">Customer ID</param>
    /// <returns>Customer information</returns>
    [HttpGet("{id:guid}")]
    public ActionResult<CustomerInfo> GetCustomer(Guid id)
    {
        try
        {
            _logger.LogDebug("Getting customer with ID: {CustomerId}", id);

            var customer = GetMockCustomers(Guid.NewGuid(), true).FirstOrDefault(c => c.Id == id);

            if (customer == null)
            {
                return NotFound($"No customer found with ID: {id}");
            }

            return Ok(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customer with ID: {CustomerId}", id);
            return Problem("An error occurred while retrieving the customer");
        }
    }

    /// <summary>
    /// Creates a new customer for a dealer
    /// </summary>
    /// <param name="request">Customer creation request</param>
    /// <returns>Created customer information</returns>
    [HttpPost]
    public ActionResult<CustomerInfo> CreateCustomer([FromBody] CreateCustomerRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrWhiteSpace(request.CompanyName))
            {
                return BadRequest("Invalid customer data");
            }

            _logger.LogDebug("Creating customer: {CompanyName}", request.CompanyName);

            var customer = new CustomerInfo
            {
                Id = Guid.NewGuid(),
                CompanyName = request.CompanyName,
                ContactNumber = request.ContactNumber ?? "",
                Email = request.Email ?? "",
                Address = request.Address ?? "",
                Active = true,
                DealerCustomer = false,
                ContractNumber = request.ContractNumber ?? "",
                ContractDate = request.ContractDate,
                Description = request.Description ?? "",
                DealerId = request.DealerId,
                CountryId = request.CountryId,
                SitesCount = 0
            };

            return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer");
            return Problem("An error occurred while creating the customer");
        }
    }

    /// <summary>
    /// Validates the relationship between a customer and dealer
    /// </summary>
    /// <param name="dealerId">Dealer ID</param>
    /// <param name="customerId">Customer ID</param>
    /// <returns>Validation result</returns>
    [HttpGet("validate")]
    public ActionResult<CustomerValidationResponse> ValidateCustomer(
        [FromQuery] Guid dealerId,
        [FromQuery] Guid customerId)
    {
        try
        {
            _logger.LogDebug("Validating customer {CustomerId} for dealer {DealerId}", customerId, dealerId);

            var response = new CustomerValidationResponse
            {
                DealerId = dealerId,
                CustomerId = customerId,
                CustomerExists = true,
                DealerExists = true,
                BelongsToDealer = true,
                IsActive = true,
                IsValid = true,
                CustomerName = "Mock Customer",
                DealerName = "Mock Dealer"
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating customer");
            return Problem("An error occurred while validating the customer");
        }
    }

    private static List<CustomerInfo> GetMockCustomers(Guid dealerId, bool activeOnly = true)
    {
        var customers = new List<CustomerInfo>
        {
            new CustomerInfo
            {
                Id = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                CompanyName = "Demo Customer 1",
                ContactNumber = "123-456-7890",
                Email = "<EMAIL>",
                Address = "123 Demo Street",
                Active = true,
                DealerCustomer = false,
                ContractNumber = "C001",
                ContractDate = DateTime.Now.AddDays(-30),
                Description = "Demo customer for testing",
                DealerId = dealerId,
                CountryId = Guid.NewGuid(),
                SitesCount = 2
            },
            new CustomerInfo
            {
                Id = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
                CompanyName = "Demo Customer 2",
                ContactNumber = "098-765-4321",
                Email = "<EMAIL>",
                Address = "456 Demo Avenue",
                Active = true,
                DealerCustomer = false,
                ContractNumber = "C002",
                ContractDate = DateTime.Now.AddDays(-60),
                Description = "Another demo customer",
                DealerId = dealerId,
                CountryId = Guid.NewGuid(),
                SitesCount = 1
            },
            new CustomerInfo
            {
                Id = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc"),
                CompanyName = "Inactive Customer",
                ContactNumber = "555-555-5555",
                Email = "<EMAIL>",
                Address = "789 Demo Road",
                Active = false,
                DealerCustomer = false,
                ContractNumber = "C003",
                ContractDate = DateTime.Now.AddDays(-90),
                Description = "Inactive customer for testing",
                DealerId = dealerId,
                CountryId = Guid.NewGuid(),
                SitesCount = 0
            }
        };

        return activeOnly ? customers.Where(c => c.Active).ToList() : customers;
    }
}

/// <summary>
/// Customer information for API responses
/// </summary>
public class CustomerInfo
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public string ContactNumber { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public bool Active { get; set; }
    public bool DealerCustomer { get; set; }
    public string ContractNumber { get; set; } = string.Empty;
    public DateTime? ContractDate { get; set; }
    public string Description { get; set; } = string.Empty;
    public Guid DealerId { get; set; }
    public Guid CountryId { get; set; }
    public short SitesCount { get; set; }
}

/// <summary>
/// Response for customer list requests
/// </summary>
public class CustomerListResponse
{
    public List<CustomerInfo> Customers { get; set; } = new();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages { get; set; }
    public Guid DealerId { get; set; }
}

/// <summary>
/// Request for creating a new customer
/// </summary>
public class CreateCustomerRequest
{
    public Guid DealerId { get; set; }
    public string CompanyName { get; set; } = string.Empty;
    public Guid CountryId { get; set; }
    public string? ContactNumber { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public string? Description { get; set; }
    public string? ContractNumber { get; set; }
    public DateTime? ContractDate { get; set; }
}

/// <summary>
/// Response for customer validation requests
/// </summary>
public class CustomerValidationResponse
{
    public Guid DealerId { get; set; }
    public Guid CustomerId { get; set; }
    public bool CustomerExists { get; set; }
    public bool DealerExists { get; set; }
    public bool BelongsToDealer { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsValid { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string DealerName { get; set; } = string.Empty;
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
}
