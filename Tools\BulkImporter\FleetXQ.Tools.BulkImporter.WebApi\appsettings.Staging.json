{"ConnectionStrings": {"DefaultConnection": "Server=staging-db-server;Database=FleetXQ_Staging;User Id=bulkimporter_app;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;", "FleetXQConnection": "Server=staging-db-server;Database=FleetXQ_Staging;User Id=bulkimporter_app;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;"}, "BulkImporter": {"DefaultDriversCount": 5000, "DefaultVehiclesCount": 2500, "DefaultBatchSize": 1000, "MaxBatchSize": 25000, "BulkCopyTimeout": 600, "CommandTimeout": 300, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": true, "UseTempTables": true, "TempTableMode": "SessionScoped", "TempTableBatchSize": 2500, "LogTempTableOperations": true}, "DataGeneration": {"OutputDirectory": "/app/data/output", "ArchiveDirectory": "/app/data/archive", "ErrorDirectory": "/app/data/errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 42, "GenerationBatchSize": 2500, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 1000}, "Environment": {"Name": "Staging", "Description": "Staging environment for testing", "RequiresApproval": true, "MaxOperationSize": 25000, "NotificationWebhooks": ["https://hooks.slack.com/services/STAGING_WEBHOOK_URL"], "MaintenanceWindows": [{"Description": "Daily maintenance window", "Start": "02:00", "End": "04:00", "TimeZone": "UTC"}]}, "Cors": {"AllowedOrigins": ["https://staging-bulkimporter.fleetxq.com", "https://staging.fleetxq.com"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Authorization", "Content-Type", "X-Requested-With"], "AllowCredentials": true}, "RateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 200}, {"Endpoint": "*/api/bulk-import/*", "Period": "1m", "Limit": 20}]}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] [{Environment}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/bulkimporter-webapi-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{Environment}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Properties": {"Application": "FleetXQ.BulkImporter.WebApi", "Environment": "Staging"}}}