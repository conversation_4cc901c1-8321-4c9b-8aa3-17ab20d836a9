<template>
  <div id="app" class="min-vh-100 d-flex flex-column">
    <!-- Header -->
    <header class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container-fluid">
        <router-link to="/" class="navbar-brand">
          <strong>FleetXQ</strong> Bulk Importer
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <span class="navbar-text">
            Environment: <span class="badge bg-light text-dark">{{ currentEnvironment }}</span>
          </span>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-auto">
      <div class="container-fluid">
        <div class="row align-items-center">
          <div class="col-md-6">
            <small class="text-muted">
              © {{ currentYear }} FleetXQ. All rights reserved.
            </small>
          </div>
          <div class="col-md-6 text-md-end">
            <small class="text-muted">
              Version 1.0.0 | Status: <span class="text-success">Online</span>
            </small>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useEnvironmentStore } from '@/stores/environment'

const environmentStore = useEnvironmentStore()

const currentEnvironment = computed(() => environmentStore.currentEnvironment?.name || 'Development')
const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.navbar-brand {
  font-size: 1.5rem;
}

.badge {
  font-size: 0.75rem;
}
</style>
