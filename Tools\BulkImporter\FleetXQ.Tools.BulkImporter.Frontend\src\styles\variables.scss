// FleetXQ Brand Colors (aligned with existing system)
$primary: #50C6D7;
$secondary: #F06445;
$success: #9ECF00;
$info: #0dcaf0;
$warning: #ffc107;
$danger: #dc3545;
$light: #f8f9fa;
$dark: #212529;

// Custom FleetXQ Colors
$fleetxq-blue: #50C6D7;
$fleetxq-orange: #F06445;
$fleetxq-green: #9ECF00;
$fleetxq-sand: #F8F6F1;
$fleetxq-dark-violet: #373950;

// Typography
$font-family-base: 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
$font-size-base: 0.875rem; // 14px
$line-height-base: 1.5;

// Spacing
$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
);

// Border radius
$border-radius: 0.375rem;
$border-radius-sm: 0.25rem;
$border-radius-lg: 0.5rem;

// Shadows
$box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
$box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
$box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

// Component specific variables
$navbar-brand-font-size: 1.5rem;
$card-border-width: 0;
$card-box-shadow: $box-shadow-sm;

// Form controls
$input-border-radius: $border-radius;
$input-focus-border-color: $primary;
$input-focus-box-shadow: 0 0 0 0.2rem rgba(80, 198, 215, 0.25);

// Buttons
$btn-border-radius: $border-radius;
$btn-font-weight: 500;

// Progress bars
$progress-height: 1rem;
$progress-border-radius: $border-radius;

// Alerts
$alert-border-radius: $border-radius;

// Cards
$card-border-radius: $border-radius;
$card-inner-border-radius: calc($border-radius - 1px);

// Compact design variables
$compact-padding-y: 0.375rem;
$compact-padding-x: 0.75rem;
$compact-margin: 0.5rem;

// Mobile-friendly touch targets
$touch-target-size: 44px;
$touch-target-spacing: 8px;
