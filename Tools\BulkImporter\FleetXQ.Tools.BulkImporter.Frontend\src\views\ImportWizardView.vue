<template>
  <div class="container-fluid py-4">
    <div class="row justify-content-center">
      <div class="col-lg-10 col-xl-8">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">Bulk Import Wizard</h4>
            <p class="text-muted mb-0">Follow the steps below to configure and execute your bulk import operation</p>
          </div>
          <div class="card-body">
            <!-- Wizard Steps Indicator -->
            <div class="wizard-steps mb-4">
              <div class="row">
                <div class="col text-center" v-for="(step, index) in wizardSteps" :key="step.id">
                  <div 
                    class="step-indicator"
                    :class="{
                      'active': currentStep === index,
                      'completed': currentStep > index,
                      'disabled': currentStep < index
                    }"
                  >
                    <div class="step-number">{{ index + 1 }}</div>
                    <div class="step-title">{{ step.title }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Step Content -->
            <div class="wizard-content">
              <!-- Step 1: Environment Selection -->
              <div v-if="currentStep === 0" class="wizard-step">
                <h5>Step 1: Environment Selection</h5>
                <p class="text-muted">Select the target environment for your bulk import operation.</p>
                
                <div class="mb-3">
                  <label for="environment" class="form-label">Environment *</label>
                  <select 
                    id="environment" 
                    class="form-select" 
                    v-model="selectedEnvironment"
                    required
                  >
                    <option value="">Select an environment...</option>
                    <option value="development">Development</option>
                    <option value="staging">Staging</option>
                    <option value="pilot">Pilot</option>
                    <option value="production">Production</option>
                  </select>
                </div>

                <div v-if="selectedEnvironment" class="alert alert-info">
                  <strong>{{ selectedEnvironment.toUpperCase() }}</strong> environment selected.
                  <span v-if="selectedEnvironment === 'production'" class="text-warning">
                    ⚠️ Production environment requires additional approval.
                  </span>
                </div>
              </div>

              <!-- Step 2: Dealer Selection -->
              <div v-if="currentStep === 1" class="wizard-step">
                <h5>Step 2: Dealer Selection</h5>
                <p class="text-muted">Select an existing dealer. New dealers cannot be created through this interface.</p>
                
                <div class="mb-3">
                  <label for="dealer" class="form-label">Dealer *</label>
                  <input 
                    type="text" 
                    id="dealer" 
                    class="form-control" 
                    v-model="dealerSearch"
                    placeholder="Search for dealer by name or subdomain..."
                    @input="searchDealers"
                  >
                </div>

                <div v-if="dealerSearch && !selectedDealer" class="alert alert-warning">
                  ⚠️ Please select an existing dealer. New dealers cannot be created.
                </div>
              </div>

              <!-- Step 3: Customer Selection -->
              <div v-if="currentStep === 2" class="wizard-step">
                <h5>Step 3: Customer Selection</h5>
                <p class="text-muted">Select a customer for the chosen dealer or create a new one if none exists.</p>
                
                <div class="mb-3">
                  <label for="customer" class="form-label">Customer</label>
                  <select 
                    id="customer" 
                    class="form-select" 
                    v-model="selectedCustomer"
                  >
                    <option value="">Select a customer...</option>
                    <option value="new">+ Create New Customer</option>
                  </select>
                </div>

                <div v-if="selectedCustomer === 'new'" class="card mt-3">
                  <div class="card-header">
                    <h6 class="mb-0">Create New Customer</h6>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <label for="customerName" class="form-label">Customer Name *</label>
                        <input 
                          type="text" 
                          id="customerName" 
                          class="form-control" 
                          v-model="newCustomer.name"
                          required
                        >
                      </div>
                      <div class="col-md-6 mb-3">
                        <label for="customerContact" class="form-label">Contact Information *</label>
                        <input 
                          type="text" 
                          id="customerContact" 
                          class="form-control" 
                          v-model="newCustomer.contact"
                          required
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Step 4: Vehicle Count -->
              <div v-if="currentStep === 3" class="wizard-step">
                <h5>Step 4: Vehicle Count</h5>
                <p class="text-muted">Specify the number of vehicles to import.</p>
                
                <div class="mb-3">
                  <label for="vehicleCount" class="form-label">No. of vehicles ({{ vehicleCount || 'xxxx' }})</label>
                  <input 
                    type="number" 
                    id="vehicleCount" 
                    class="form-control" 
                    v-model.number="vehicleCount"
                    min="1"
                    max="10000"
                    placeholder="Enter vehicle count"
                  >
                  <div class="form-text">Maximum: 10,000 vehicles</div>
                </div>
              </div>

              <!-- Step 5: Driver Count -->
              <div v-if="currentStep === 4" class="wizard-step">
                <h5>Step 5: Driver Count</h5>
                <p class="text-muted">Specify the number of drivers to import.</p>
                
                <div class="mb-3">
                  <label for="driverCount" class="form-label">No. of drivers ({{ driverCount || 'xxxx' }})</label>
                  <input 
                    type="number" 
                    id="driverCount" 
                    class="form-control" 
                    v-model.number="driverCount"
                    min="1"
                    max="5000"
                    placeholder="Enter driver count"
                  >
                  <div class="form-text">Maximum: 5,000 drivers</div>
                </div>
              </div>
            </div>

            <!-- Navigation -->
            <div class="wizard-navigation d-flex justify-content-between">
              <button 
                class="btn btn-outline-secondary" 
                @click="previousStep"
                :disabled="currentStep === 0"
              >
                Previous
              </button>
              
              <button 
                v-if="currentStep < wizardSteps.length - 1"
                class="btn btn-primary" 
                @click="nextStep"
                :disabled="!canProceed"
              >
                Next
              </button>
              
              <button 
                v-else
                class="btn btn-success" 
                @click="startImport"
                :disabled="!canProceed"
              >
                Start Import
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Wizard state
const currentStep = ref(0)
const wizardSteps = [
  { id: 'environment', title: 'Environment' },
  { id: 'dealer', title: 'Dealer' },
  { id: 'customer', title: 'Customer' },
  { id: 'vehicles', title: 'Vehicles' },
  { id: 'drivers', title: 'Drivers' }
]

// Form data
const selectedEnvironment = ref('')
const dealerSearch = ref('')
const selectedDealer = ref('')
const selectedCustomer = ref('')
const newCustomer = ref({
  name: '',
  contact: ''
})
const vehicleCount = ref<number | null>(null)
const driverCount = ref<number | null>(null)

// Computed properties
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0: return selectedEnvironment.value !== ''
    case 1: return selectedDealer.value !== ''
    case 2: return selectedCustomer.value !== '' && (selectedCustomer.value !== 'new' || (newCustomer.value.name && newCustomer.value.contact))
    case 3: return vehicleCount.value && vehicleCount.value > 0 && vehicleCount.value <= 10000
    case 4: return driverCount.value && driverCount.value > 0 && driverCount.value <= 5000
    default: return false
  }
})

// Methods
const nextStep = () => {
  if (currentStep.value < wizardSteps.length - 1) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const searchDealers = () => {
  // TODO: Implement dealer search
  console.log('Searching dealers:', dealerSearch.value)
}

const startImport = () => {
  // TODO: Implement import start
  console.log('Starting import with:', {
    environment: selectedEnvironment.value,
    dealer: selectedDealer.value,
    customer: selectedCustomer.value,
    newCustomer: newCustomer.value,
    vehicleCount: vehicleCount.value,
    driverCount: driverCount.value
  })
}
</script>

<style scoped>
.wizard-steps {
  .step-indicator {
    position: relative;
    
    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #e9ecef;
      color: #6c757d;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 0.5rem;
      font-weight: bold;
    }
    
    .step-title {
      font-size: 0.875rem;
      color: #6c757d;
    }
    
    &.active {
      .step-number {
        background-color: var(--bs-primary);
        color: white;
      }
      
      .step-title {
        color: var(--bs-primary);
        font-weight: 500;
      }
    }
    
    &.completed {
      .step-number {
        background-color: var(--bs-success);
        color: white;
      }
      
      .step-title {
        color: var(--bs-success);
      }
    }
  }
}

.wizard-step {
  min-height: 300px;
  padding: 2rem 0;
}

.wizard-navigation {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
  margin-top: 2rem;
}
</style>
