namespace FleetXQ.Tools.BulkImporter.Core.Services;

/// <summary>
/// Service interface for bulk import operations
/// </summary>
public interface IBulkImportService
{
    /// <summary>
    /// Executes the bulk import operation
    /// </summary>
    /// <param name="options">Import options and parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Import result summary</returns>
    Task<ImportResult> ExecuteImportAsync(ImportOptions options, CancellationToken cancellationToken = default);
}

/// <summary>
/// Options for bulk import operation
/// </summary>
public class ImportOptions
{
    public int? DriversCount { get; set; }
    public int? VehiclesCount { get; set; }
    public int? BatchSize { get; set; }
    public bool DryRun { get; set; }
    public bool GenerateData { get; set; }
    public bool Interactive { get; set; } = true;

}

/// <summary>
/// Result of bulk import operation
/// </summary>
public class ImportResult
{
    public bool Success { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public int TotalRows { get; set; }
    public int ProcessedRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}
