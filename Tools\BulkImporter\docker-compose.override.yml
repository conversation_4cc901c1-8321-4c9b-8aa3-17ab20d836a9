version: '3.8'

services:
  bulkimporter-api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__FleetXQConnection=Server=sql-server,1433;Database=FleetXQ;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;MultipleActiveResultSets=true;
      - ConnectionStrings__DefaultConnection=Server=sql-server,1433;Database=FleetXQ;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;MultipleActiveResultSets=true;
    volumes:
      - ./FleetXQ.Tools.BulkImporter.WebApi:/app:ro
      - ./FleetXQ.Tools.BulkImporter.Core:/core:ro
    ports:
      - "5000:80"

  bulkimporter-frontend:
    volumes:
      - ./FleetXQ.Tools.BulkImporter.Frontend/src:/app/src:ro
    ports:
      - "3000:8080"

  sql-server:
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
