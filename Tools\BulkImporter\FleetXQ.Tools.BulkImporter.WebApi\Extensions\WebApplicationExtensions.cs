using AspNetCoreRateLimit;
using FleetXQ.Tools.BulkImporter.WebApi.Hubs;
using FleetXQ.Tools.BulkImporter.WebApi.Middleware;

namespace FleetXQ.Tools.BulkImporter.WebApi.Extensions;

/// <summary>
/// Extension methods for configuring the Web API pipeline
/// </summary>
public static class WebApplicationExtensions
{
    /// <summary>
    /// Configures the HTTP request pipeline for the Bulk Importer Web API
    /// </summary>
    public static WebApplication ConfigureBulkImporterPipeline(this WebApplication app)
    {
        // Configure the HTTP request pipeline
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "FleetXQ Bulk Importer API v1");
                c.RoutePrefix = string.Empty; // Serve Swagger UI at root
            });
        }

        // Security headers
        app.UseHsts();
        app.UseHttpsRedirection();

        // CORS
        app.UseCors();

        // Rate limiting
        app.UseIpRateLimiting();

        // Custom middleware
        app.UseMiddleware<RequestLoggingMiddleware>();
        app.UseMiddleware<ErrorHandlingMiddleware>();

        // Authentication & Authorization
        app.UseAuthentication();
        app.UseAuthorization();

        // Static files (for serving frontend if needed)
        app.UseStaticFiles();

        // Routing
        app.UseRouting();

        // Map controllers
        app.MapControllers();

        // Map SignalR hubs
        app.MapHub<ImportProgressHub>("/hubs/import-progress");

        // Map health checks
        app.MapHealthChecks("/health");
        app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
        {
            Predicate = check => check.Tags.Contains("ready")
        });
        app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
        {
            Predicate = _ => false
        });

        return app;
    }
}
