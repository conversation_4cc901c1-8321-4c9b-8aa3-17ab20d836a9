# FleetXQ Bulk Importer - Web API + Vue.js Frontend

This is the modernized version of the FleetXQ Bulk Importer, transformed from a console application into a web-based solution with a RESTful API and Vue.js frontend.

## Project Structure

```
FleetXQ.Tools.BulkImporter/
├── FleetXQ.Tools.BulkImporter.Core/          # Shared business logic and models
│   ├── Configuration/                         # Configuration classes and validation
│   ├── Services/                             # Business services (to be moved from console app)
│   └── Models/                               # Data models and DTOs
├── FleetXQ.Tools.BulkImporter.WebApi/        # ASP.NET Core Web API
│   ├── Controllers/                          # API controllers
│   ├── Extensions/                           # Service configuration extensions
│   ├── Middleware/                           # Custom middleware
│   ├── Hubs/                                # SignalR hubs for real-time updates
│   └── Dockerfile                           # Docker configuration for API
├── FleetXQ.Tools.BulkImporter.Frontend/      # Vue.js 3 frontend application
│   ├── src/
│   │   ├── components/                       # Vue components
│   │   ├── views/                           # Page views
│   │   ├── stores/                          # Pinia state management
│   │   ├── services/                        # API service layer
│   │   ├── types/                           # TypeScript type definitions
│   │   └── styles/                          # SCSS stylesheets
│   ├── Dockerfile                           # Docker configuration for frontend
│   └── nginx.conf                           # Nginx configuration
├── BulkImporter.csproj                       # Original console application (maintained)
├── docker-compose.yml                        # Docker Compose configuration
├── docker-compose.override.yml               # Development overrides
└── FleetXQ.Tools.BulkImporter.sln            # Visual Studio solution file
```

## Technology Stack

### Backend (Web API)
- **Framework**: ASP.NET Core 6.0
- **Database**: SQL Server with existing staging schema
- **Real-time**: SignalR for progress updates
- **Authentication**: JWT Bearer tokens
- **Documentation**: Swagger/OpenAPI
- **Logging**: Serilog with structured logging
- **Caching**: Redis (optional)

### Frontend (Vue.js)
- **Framework**: Vue.js 3 with Composition API
- **Build Tool**: Vite
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **HTTP Client**: Axios
- **UI Framework**: Bootstrap 5
- **Language**: TypeScript
- **Real-time**: SignalR client

### Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Reverse Proxy**: Nginx (for frontend)
- **Database**: SQL Server 2022
- **Caching**: Redis 7

## Quick Start

### Prerequisites
- Docker and Docker Compose
- .NET 6.0 SDK (for local development)
- Node.js 18+ (for frontend development)

### Development Setup

1. **Clone and navigate to the project**:
   ```bash
   cd Tools/BulkImporter
   ```

2. **Start the development environment**:
   ```bash
   docker-compose up -d
   ```

3. **Access the applications**:
   - Frontend: http://localhost:3000
   - API: http://localhost:5000
   - API Documentation: http://localhost:5000/swagger
   - SQL Server: localhost:1433 (sa/YourStrong@Passw0rd)

### Local Development (without Docker)

1. **Start the Web API**:
   ```bash
   cd FleetXQ.Tools.BulkImporter.WebApi
   dotnet run
   ```

2. **Start the Frontend** (in a new terminal):
   ```bash
   cd FleetXQ.Tools.BulkImporter.Frontend
   npm install
   npm run dev
   ```

## Architecture Overview

### API Endpoints (Planned)
- `GET /api/environments` - Environment management
- `GET /api/dealers` - Dealer lookup and validation
- `GET /api/customers` - Customer management
- `POST /api/bulk-import/sessions` - Create import sessions
- `GET /api/bulk-import/sessions/{id}/progress` - Real-time progress
- `/hubs/import-progress` - SignalR hub for live updates

### User Flow
1. **Environment Selection**: Choose target environment (dev/pilot/production)
2. **Dealer Selection**: Select existing dealer (with validation)
3. **Customer Selection**: Choose customer or create new one
4. **Count Inputs**: Specify vehicle and driver counts
5. **Import Execution**: Real-time progress tracking
6. **Results Review**: Comprehensive import results

## Configuration

### Environment Variables
- `ASPNETCORE_ENVIRONMENT`: Environment name (Development/Staging/Pilot/Production)
- `ConnectionStrings__FleetXQConnection`: Database connection string
- `BulkImporter__*`: Bulk import configuration options
- `Cors__AllowedOrigins`: CORS allowed origins for frontend

### Docker Environment
The Docker setup includes:
- SQL Server with FleetXQ database
- Redis for caching and SignalR backplane
- Web API with health checks
- Frontend with Nginx reverse proxy

## Security Features

- JWT Bearer token authentication
- CORS configuration for frontend integration
- Rate limiting to prevent abuse
- Input validation and sanitization
- Structured error handling
- Security headers (HSTS, XSS protection, etc.)

## Monitoring and Logging

- Structured logging with Serilog
- Health checks for API and database
- Request/response logging middleware
- Performance monitoring
- Error tracking and alerting

## Migration from Console Application

The original console application (`BulkImporter.csproj`) is maintained alongside the new web solution to ensure:
- Backward compatibility
- Gradual migration path
- Fallback option for critical operations
- Shared business logic through the Core project

## Next Steps (Phase 1.2)

1. Move existing services from console app to Core project
2. Implement API controllers for environment, dealer, and customer management
3. Create Vue.js components for the import wizard
4. Set up SignalR for real-time progress updates
5. Implement comprehensive testing

## Development Guidelines

- Follow existing FleetXQ coding standards
- Use the existing database schema and stored procedures
- Maintain compatibility with existing dealer and customer APIs
- Implement comprehensive error handling and validation
- Write unit and integration tests for all new functionality

## Support

For questions or issues with the new web-based bulk importer:
1. Check the existing console application documentation
2. Review the todo.md file for implementation roadmap
3. Consult the API documentation at `/swagger`
4. Contact the FleetXQ development team

---

*This project represents Phase 1.1 of the bulk importer modernization initiative. See todo.md for the complete implementation roadmap.*
