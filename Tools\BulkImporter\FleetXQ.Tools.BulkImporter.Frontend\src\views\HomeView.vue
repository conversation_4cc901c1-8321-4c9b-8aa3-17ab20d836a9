<template>
  <div class="container-fluid py-4">
    <div class="row">
      <div class="col-12">
        <h1 class="h3 mb-4">FleetXQ Bulk Importer Dashboard</h1>
        
        <!-- Quick Stats -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="card bg-primary text-white">
              <div class="card-body">
                <div class="d-flex justify-content-between">
                  <div>
                    <h5 class="card-title">Active Sessions</h5>
                    <h2 class="mb-0">{{ stats.activeSessions }}</h2>
                  </div>
                  <div class="align-self-center">
                    <i class="fas fa-play-circle fa-2x"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="card bg-success text-white">
              <div class="card-body">
                <div class="d-flex justify-content-between">
                  <div>
                    <h5 class="card-title">Completed Today</h5>
                    <h2 class="mb-0">{{ stats.completedToday }}</h2>
                  </div>
                  <div class="align-self-center">
                    <i class="fas fa-check-circle fa-2x"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="card bg-warning text-white">
              <div class="card-body">
                <div class="d-flex justify-content-between">
                  <div>
                    <h5 class="card-title">Total Records</h5>
                    <h2 class="mb-0">{{ stats.totalRecords.toLocaleString() }}</h2>
                  </div>
                  <div class="align-self-center">
                    <i class="fas fa-database fa-2x"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="card bg-info text-white">
              <div class="card-body">
                <div class="d-flex justify-content-between">
                  <div>
                    <h5 class="card-title">Environment</h5>
                    <h2 class="mb-0">{{ currentEnvironment }}</h2>
                  </div>
                  <div class="align-self-center">
                    <i class="fas fa-server fa-2x"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6 col-lg-3 mb-3">
                    <router-link to="/import" class="btn btn-primary btn-lg w-100">
                      <i class="fas fa-upload me-2"></i>
                      Start New Import
                    </router-link>
                  </div>
                  <div class="col-md-6 col-lg-3 mb-3">
                    <router-link to="/sessions" class="btn btn-outline-primary btn-lg w-100">
                      <i class="fas fa-history me-2"></i>
                      View Sessions
                    </router-link>
                  </div>
                  <div class="col-md-6 col-lg-3 mb-3">
                    <button class="btn btn-outline-secondary btn-lg w-100" @click="refreshStats">
                      <i class="fas fa-sync-alt me-2"></i>
                      Refresh Stats
                    </button>
                  </div>
                  <div class="col-md-6 col-lg-3 mb-3">
                    <router-link to="/settings" class="btn btn-outline-secondary btn-lg w-100">
                      <i class="fas fa-cog me-2"></i>
                      Settings
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title mb-0">Recent Activity</h5>
              </div>
              <div class="card-body">
                <div v-if="recentActivity.length === 0" class="text-center text-muted py-4">
                  <i class="fas fa-inbox fa-3x mb-3"></i>
                  <p>No recent activity</p>
                </div>
                <div v-else class="list-group list-group-flush">
                  <div 
                    v-for="activity in recentActivity" 
                    :key="activity.id"
                    class="list-group-item d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <strong>{{ activity.title }}</strong>
                      <p class="mb-0 text-muted">{{ activity.description }}</p>
                    </div>
                    <div class="text-end">
                      <span :class="`badge bg-${activity.status === 'completed' ? 'success' : activity.status === 'failed' ? 'danger' : 'warning'}`">
                        {{ activity.status }}
                      </span>
                      <br>
                      <small class="text-muted">{{ formatDate(activity.timestamp) }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useEnvironmentStore } from '@/stores/environment'

const environmentStore = useEnvironmentStore()

// Mock data - will be replaced with real API calls
const stats = ref({
  activeSessions: 2,
  completedToday: 15,
  totalRecords: 125000,
})

const recentActivity = ref([
  {
    id: 1,
    title: 'Bulk Import Session #12345',
    description: '5,000 drivers and 2,500 vehicles imported successfully',
    status: 'completed',
    timestamp: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
  },
  {
    id: 2,
    title: 'Bulk Import Session #12344',
    description: '1,200 drivers imported with 3 validation errors',
    status: 'warning',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
  }
])

const currentEnvironment = computed(() => 
  environmentStore.currentEnvironment?.name || 'Development'
)

const refreshStats = async () => {
  // TODO: Implement actual stats refresh
  console.log('Refreshing stats...')
}

const formatDate = (date: Date) => {
  return new Intl.RelativeTimeFormatter('en', { numeric: 'auto' }).format(
    Math.round((date.getTime() - Date.now()) / (1000 * 60)),
    'minute'
  )
}

onMounted(() => {
  environmentStore.loadSavedEnvironment()
})
</script>

<style scoped>
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
}

.list-group-item {
  border-left: none;
  border-right: none;
}

.list-group-item:first-child {
  border-top: none;
}

.list-group-item:last-child {
  border-bottom: none;
}
</style>
