using FleetXQ.Tools.BulkImporter.Core.Configuration;
using FleetXQ.Tools.BulkImporter.WebApi.Extensions;
using Serilog;

namespace FleetXQ.Tools.BulkImporter.WebApi;

/// <summary>
/// FleetXQ Bulk Import Web API Application
/// Provides RESTful API endpoints for bulk import operations with Vue.js frontend support.
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // Create initial logger for startup
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateBootstrapLogger();

        try
        {
            Log.Information("Starting FleetXQ Bulk Importer Web API");

            var builder = WebApplication.CreateBuilder(args);

            // Configure Serilog
            builder.Host.UseSerilog((context, services, configuration) => configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services)
                .Enrich.FromLogContext()
                .Enrich.WithMachineName()
                .Enrich.WithThreadId()
                .Enrich.WithEnvironmentName());

            // Add services to the container
            builder.Services.AddBulkImporterCore(builder.Configuration);
            builder.Services.AddBulkImporterWebApi(builder.Configuration);

            var app = builder.Build();

            // Configure the HTTP request pipeline
            app.ConfigureBulkImporterPipeline();

            Log.Information("FleetXQ Bulk Importer Web API configured successfully");

            await app.RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "FleetXQ Bulk Importer Web API terminated unexpectedly");
            return 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }
}
