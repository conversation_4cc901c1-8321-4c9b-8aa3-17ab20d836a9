using Microsoft.AspNetCore.Mvc;
using FleetXQ.Tools.BulkImporter.Core.Services;
using FleetXQ.Tools.BulkImporter.Core.Configuration;
using Microsoft.Extensions.Options;

namespace FleetXQ.Tools.BulkImporter.WebApi.Controllers;

/// <summary>
/// Controller for data generation and validation operations
/// </summary>
[ApiController]
[Route("api/data-generation")]
[Produces("application/json")]
public class DataGenerationController : ControllerBase
{
    private readonly ISqlDataGenerationService _dataGenerationService;
    private readonly IEnvironmentService _environmentService;
    private readonly BulkImporterOptions _options;
    private readonly DataGenerationOptions _generationOptions;
    private readonly ILogger<DataGenerationController> _logger;

    public DataGenerationController(
        ISqlDataGenerationService dataGenerationService,
        IEnvironmentService environmentService,
        IOptions<BulkImporterOptions> options,
        IOptions<DataGenerationOptions> generationOptions,
        ILogger<DataGenerationController> logger)
    {
        _dataGenerationService = dataGenerationService ?? throw new ArgumentNullException(nameof(dataGenerationService));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _generationOptions = generationOptions?.Value ?? throw new ArgumentNullException(nameof(generationOptions));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Validates import parameters before execution
    /// </summary>
    /// <param name="request">Import parameters to validate</param>
    /// <returns>Validation result with detailed feedback</returns>
    /// <response code="200">Returns the validation result</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(ImportValidationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ImportValidationResponse>> ValidateImportParameters([FromBody] ImportValidationRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Validating import parameters: dealers: {DealerId}, customer: {CustomerId}, drivers: {DriversCount}, vehicles: {VehiclesCount}",
                request.DealerId, request.CustomerId, request.DriversCount, request.VehiclesCount);

            var response = new ImportValidationResponse
            {
                DealerId = request.DealerId,
                CustomerId = request.CustomerId,
                DriversCount = request.DriversCount,
                VehiclesCount = request.VehiclesCount,
                BatchSize = request.BatchSize ?? _options.DefaultBatchSize
            };

            // Basic parameter validation
            if (request.DealerId == Guid.Empty)
                response.ValidationErrors.Add("DealerId is required");

            if (request.CustomerId == Guid.Empty)
                response.ValidationErrors.Add("CustomerId is required");

            if (request.DriversCount <= 0)
                response.ValidationErrors.Add("DriversCount must be greater than 0");

            if (request.VehiclesCount <= 0)
                response.ValidationErrors.Add("VehiclesCount must be greater than 0");

            if (request.BatchSize.HasValue && (request.BatchSize.Value < 100 || request.BatchSize.Value > _options.MaxBatchSize))
                response.ValidationErrors.Add($"BatchSize must be between 100 and {_options.MaxBatchSize}");

            // Environment validation
            var totalOperationSize = request.DriversCount + request.VehiclesCount;
            var environmentValidation = _environmentService.ValidateOperation(totalOperationSize);

            if (!environmentValidation.IsValid)
            {
                response.ValidationErrors.Add(environmentValidation.ErrorMessage ?? "Environment validation failed");
            }

            response.ValidationWarnings.AddRange(environmentValidation.Warnings);
            response.RequiresApproval = environmentValidation.RequiresApproval;

            // Memory and performance validation
            var estimatedMemoryMB = EstimateMemoryUsage(request.DriversCount, request.VehiclesCount);
            if (estimatedMemoryMB > _generationOptions.MaxMemoryUsageMB)
            {
                response.ValidationWarnings.Add($"Estimated memory usage ({estimatedMemoryMB:N0} MB) exceeds recommended limit ({_generationOptions.MaxMemoryUsageMB:N0} MB)");
            }

            // Batch size recommendations
            var recommendedBatchSize = CalculateRecommendedBatchSize(totalOperationSize);
            if (request.BatchSize.HasValue && Math.Abs(request.BatchSize.Value - recommendedBatchSize) > recommendedBatchSize * 0.5)
            {
                response.ValidationWarnings.Add($"Consider using batch size around {recommendedBatchSize:N0} for optimal performance");
            }

            response.IsValid = !response.ValidationErrors.Any();
            response.EstimatedDuration = EstimateProcessingDuration(request.DriversCount, request.VehiclesCount, response.BatchSize);
            response.EstimatedMemoryUsageMB = estimatedMemoryMB;
            response.RecommendedBatchSize = recommendedBatchSize;

            _logger.LogDebug("Import validation completed: {IsValid}, errors: {ErrorCount}, warnings: {WarningCount}",
                response.IsValid, response.ValidationErrors.Count, response.ValidationWarnings.Count);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating import parameters");
            return Problem(
                title: "Error validating import parameters",
                detail: "An error occurred while validating the import parameters",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Generates preview data for import operations
    /// </summary>
    /// <param name="request">Preview generation request</param>
    /// <returns>Preview data samples</returns>
    /// <response code="200">Returns the preview data</response>
    /// <response code="400">If the request is invalid</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpPost("preview")]
    [ProducesResponseType(typeof(PreviewDataResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<PreviewDataResponse>> GeneratePreviewData([FromBody] PreviewDataRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid request",
                    Detail = "Request body cannot be null",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            // Validate request
            if (request.SampleSize < 1 || request.SampleSize > 100)
            {
                return BadRequest(new ProblemDetails
                {
                    Title = "Invalid sample size",
                    Detail = "Sample size must be between 1 and 100",
                    Status = StatusCodes.Status400BadRequest
                });
            }

            _logger.LogDebug("Generating preview data: {SampleSize} samples, includeDrivers: {IncludeDrivers}, includeVehicles: {IncludeVehicles}",
                request.SampleSize, request.IncludeDrivers, request.IncludeVehicles);

            var response = new PreviewDataResponse
            {
                SampleSize = request.SampleSize,
                GeneratedAt = DateTime.UtcNow
            };

            // Generate preview session for temporary data
            var previewSessionId = Guid.NewGuid();

            try
            {
                // Generate sample driver data if requested
                if (request.IncludeDrivers)
                {
                    var driverResult = await _dataGenerationService.GenerateDriverDataAsync(previewSessionId, request.SampleSize);
                    if (driverResult.Success)
                    {
                        response.DriverSamples = await GetDriverPreviewSamples(previewSessionId, request.SampleSize);
                    }
                    else
                    {
                        response.Errors.AddRange(driverResult.Errors);
                    }
                }

                // Generate sample vehicle data if requested
                if (request.IncludeVehicles)
                {
                    var vehicleResult = await _dataGenerationService.GenerateVehicleDataAsync(previewSessionId, request.SampleSize);
                    if (vehicleResult.Success)
                    {
                        response.VehicleSamples = await GetVehiclePreviewSamples(previewSessionId, request.SampleSize);
                    }
                    else
                    {
                        response.Errors.AddRange(vehicleResult.Errors);
                    }
                }

                response.Success = !response.Errors.Any();

                _logger.LogDebug("Preview data generated: {Success}, drivers: {DriverCount}, vehicles: {VehicleCount}",
                    response.Success, response.DriverSamples.Count, response.VehicleSamples.Count);

                return Ok(response);
            }
            finally
            {
                // Clean up preview session data
                try
                {
                    await CleanupPreviewSession(previewSessionId);
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "Failed to cleanup preview session: {SessionId}", previewSessionId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating preview data");
            return Problem(
                title: "Error generating preview data",
                detail: "An error occurred while generating preview data",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Gets available data templates and schemas
    /// </summary>
    /// <returns>Data templates and field schemas</returns>
    /// <response code="200">Returns the data templates</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("templates")]
    [ProducesResponseType(typeof(DataTemplatesResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public ActionResult<DataTemplatesResponse> GetDataTemplates()
    {
        try
        {
            _logger.LogDebug("Getting data templates and schemas");

            var response = new DataTemplatesResponse
            {
                DriverTemplate = GetDriverTemplate(),
                VehicleTemplate = GetVehicleTemplate(),
                ValidationRules = GetValidationRules(),
                SupportedFormats = new List<string> { "CSV", "JSON", "SQL" },
                MaxBatchSize = _options.MaxBatchSize,
                RecommendedBatchSize = _options.DefaultBatchSize
            };

            _logger.LogDebug("Retrieved data templates: driver fields: {DriverFields}, vehicle fields: {VehicleFields}",
                response.DriverTemplate.Fields.Count, response.VehicleTemplate.Fields.Count);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving data templates");
            return Problem(
                title: "Error retrieving data templates",
                detail: "An error occurred while retrieving data templates",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static int EstimateMemoryUsage(int driversCount, int vehiclesCount)
    {
        // Rough estimation: ~1KB per driver, ~2KB per vehicle
        return (driversCount + (vehiclesCount * 2)) / 1024;
    }

    private static int CalculateRecommendedBatchSize(int totalOperationSize)
    {
        return totalOperationSize switch
        {
            <= 1000 => 500,
            <= 5000 => 1000,
            <= 10000 => 2000,
            <= 50000 => 5000,
            _ => 10000
        };
    }

    private static TimeSpan EstimateProcessingDuration(int driversCount, int vehiclesCount, int batchSize)
    {
        // Rough estimation: ~100 records per second
        var totalRecords = driversCount + vehiclesCount;
        var estimatedSeconds = Math.Max(1, totalRecords / 100);
        return TimeSpan.FromSeconds(estimatedSeconds);
    }

    private async Task<List<DriverPreviewSample>> GetDriverPreviewSamples(Guid sessionId, int sampleSize)
    {
        // In a real implementation, this would query the staging tables for sample data
        // For now, return mock data
        var samples = new List<DriverPreviewSample>();
        for (int i = 1; i <= sampleSize; i++)
        {
            samples.Add(new DriverPreviewSample
            {
                ExternalDriverId = $"DRV{i:D6}",
                PersonFirstName = $"Driver{i}",
                PersonLastName = "Sample",
                PersonEmail = $"driver{i}@example.com",
                CustomerName = "Sample Customer",
                SiteName = "Sample Site",
                DepartmentName = "Sample Department"
            });
        }
        return samples;
    }

    private async Task<List<VehiclePreviewSample>> GetVehiclePreviewSamples(Guid sessionId, int sampleSize)
    {
        // In a real implementation, this would query the staging tables for sample data
        // For now, return mock data
        var samples = new List<VehiclePreviewSample>();
        for (int i = 1; i <= sampleSize; i++)
        {
            samples.Add(new VehiclePreviewSample
            {
                ExternalVehicleId = $"VEH{i:D6}",
                HireNo = $"H{i:D4}",
                SerialNo = $"SN{i:D8}",
                Description = $"Sample Vehicle {i}",
                CustomerName = "Sample Customer",
                SiteName = "Sample Site",
                ModelName = "Sample Model",
                ManufacturerName = "Sample Manufacturer"
            });
        }
        return samples;
    }

    private async Task CleanupPreviewSession(Guid sessionId)
    {
        // In a real implementation, this would clean up temporary staging data
        // For now, this is a no-op since we're using mock data
        await Task.CompletedTask;
    }

    private static DataTemplate GetDriverTemplate()
    {
        return new DataTemplate
        {
            Name = "Driver",
            Description = "Template for driver import data",
            Fields = new List<FieldDefinition>
            {
                new() { Name = "ExternalDriverId", Type = "string", Required = true, MaxLength = 50, Description = "Unique external identifier for the driver" },
                new() { Name = "PersonFirstName", Type = "string", Required = true, MaxLength = 50, Description = "Driver's first name" },
                new() { Name = "PersonLastName", Type = "string", Required = true, MaxLength = 50, Description = "Driver's last name" },
                new() { Name = "PersonEmail", Type = "string", Required = false, MaxLength = 100, Description = "Driver's email address" },
                new() { Name = "CustomerName", Type = "string", Required = true, MaxLength = 50, Description = "Customer name" },
                new() { Name = "SiteName", Type = "string", Required = true, MaxLength = 50, Description = "Site name" },
                new() { Name = "DepartmentName", Type = "string", Required = false, MaxLength = 50, Description = "Department name" }
            }
        };
    }

    private static DataTemplate GetVehicleTemplate()
    {
        return new DataTemplate
        {
            Name = "Vehicle",
            Description = "Template for vehicle import data",
            Fields = new List<FieldDefinition>
            {
                new() { Name = "ExternalVehicleId", Type = "string", Required = true, MaxLength = 50, Description = "Unique external identifier for the vehicle" },
                new() { Name = "HireNo", Type = "string", Required = true, MaxLength = 20, Description = "Hire number" },
                new() { Name = "SerialNo", Type = "string", Required = true, MaxLength = 50, Description = "Serial number" },
                new() { Name = "Description", Type = "string", Required = false, MaxLength = 100, Description = "Vehicle description" },
                new() { Name = "CustomerName", Type = "string", Required = true, MaxLength = 50, Description = "Customer name" },
                new() { Name = "SiteName", Type = "string", Required = true, MaxLength = 50, Description = "Site name" },
                new() { Name = "ModelName", Type = "string", Required = true, MaxLength = 50, Description = "Model name" },
                new() { Name = "ManufacturerName", Type = "string", Required = true, MaxLength = 50, Description = "Manufacturer name" }
            }
        };
    }

    private static ValidationRules GetValidationRules()
    {
        return new ValidationRules
        {
            General = new List<string>
            {
                "All required fields must be provided",
                "Field lengths must not exceed specified maximums",
                "Email addresses must be in valid format",
                "External IDs must be unique within the import session"
            },
            Driver = new List<string>
            {
                "PersonFirstName and PersonLastName are required",
                "CustomerName and SiteName must exist in the system",
                "ExternalDriverId must be unique"
            },
            Vehicle = new List<string>
            {
                "HireNo and SerialNo are required",
                "CustomerName and SiteName must exist in the system",
                "ModelName and ManufacturerName must exist in the system",
                "ExternalVehicleId must be unique"
            }
        };
    }
}

/// <summary>
/// Request for validating import parameters
/// </summary>
public class ImportValidationRequest
{
    /// <summary>
    /// Dealer ID for the import operation
    /// </summary>
    public Guid DealerId { get; set; }

    /// <summary>
    /// Customer ID for the import operation
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Number of drivers to import
    /// </summary>
    public int DriversCount { get; set; }

    /// <summary>
    /// Number of vehicles to import
    /// </summary>
    public int VehiclesCount { get; set; }

    /// <summary>
    /// Batch size for processing (optional)
    /// </summary>
    public int? BatchSize { get; set; }
}

/// <summary>
/// Response for import parameter validation
/// </summary>
public class ImportValidationResponse
{
    /// <summary>
    /// Dealer ID that was validated
    /// </summary>
    public Guid DealerId { get; set; }

    /// <summary>
    /// Customer ID that was validated
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Number of drivers to import
    /// </summary>
    public int DriversCount { get; set; }

    /// <summary>
    /// Number of vehicles to import
    /// </summary>
    public int VehiclesCount { get; set; }

    /// <summary>
    /// Batch size for processing
    /// </summary>
    public int BatchSize { get; set; }

    /// <summary>
    /// Whether the parameters are valid
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Whether the operation requires approval
    /// </summary>
    public bool RequiresApproval { get; set; }

    /// <summary>
    /// List of validation errors
    /// </summary>
    public List<string> ValidationErrors { get; set; } = new();

    /// <summary>
    /// List of validation warnings
    /// </summary>
    public List<string> ValidationWarnings { get; set; } = new();

    /// <summary>
    /// Estimated processing duration
    /// </summary>
    public TimeSpan EstimatedDuration { get; set; }

    /// <summary>
    /// Estimated memory usage in MB
    /// </summary>
    public int EstimatedMemoryUsageMB { get; set; }

    /// <summary>
    /// Recommended batch size for optimal performance
    /// </summary>
    public int RecommendedBatchSize { get; set; }
}

/// <summary>
/// Request for generating preview data
/// </summary>
public class PreviewDataRequest
{
    /// <summary>
    /// Number of sample records to generate (1-100)
    /// </summary>
    public int SampleSize { get; set; } = 10;

    /// <summary>
    /// Whether to include driver samples
    /// </summary>
    public bool IncludeDrivers { get; set; } = true;

    /// <summary>
    /// Whether to include vehicle samples
    /// </summary>
    public bool IncludeVehicles { get; set; } = true;
}

/// <summary>
/// Response containing preview data samples
/// </summary>
public class PreviewDataResponse
{
    /// <summary>
    /// Number of samples generated
    /// </summary>
    public int SampleSize { get; set; }

    /// <summary>
    /// Whether the preview generation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// When the preview was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// Sample driver records
    /// </summary>
    public List<DriverPreviewSample> DriverSamples { get; set; } = new();

    /// <summary>
    /// Sample vehicle records
    /// </summary>
    public List<VehiclePreviewSample> VehicleSamples { get; set; } = new();

    /// <summary>
    /// Any errors encountered during generation
    /// </summary>
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Sample driver record for preview
/// </summary>
public class DriverPreviewSample
{
    public string ExternalDriverId { get; set; } = string.Empty;
    public string PersonFirstName { get; set; } = string.Empty;
    public string PersonLastName { get; set; } = string.Empty;
    public string PersonEmail { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string SiteName { get; set; } = string.Empty;
    public string DepartmentName { get; set; } = string.Empty;
}

/// <summary>
/// Sample vehicle record for preview
/// </summary>
public class VehiclePreviewSample
{
    public string ExternalVehicleId { get; set; } = string.Empty;
    public string HireNo { get; set; } = string.Empty;
    public string SerialNo { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string SiteName { get; set; } = string.Empty;
    public string ModelName { get; set; } = string.Empty;
    public string ManufacturerName { get; set; } = string.Empty;
}

/// <summary>
/// Response containing data templates and schemas
/// </summary>
public class DataTemplatesResponse
{
    /// <summary>
    /// Driver data template
    /// </summary>
    public DataTemplate DriverTemplate { get; set; } = new();

    /// <summary>
    /// Vehicle data template
    /// </summary>
    public DataTemplate VehicleTemplate { get; set; } = new();

    /// <summary>
    /// Validation rules for import data
    /// </summary>
    public ValidationRules ValidationRules { get; set; } = new();

    /// <summary>
    /// Supported data formats
    /// </summary>
    public List<string> SupportedFormats { get; set; } = new();

    /// <summary>
    /// Maximum allowed batch size
    /// </summary>
    public int MaxBatchSize { get; set; }

    /// <summary>
    /// Recommended batch size
    /// </summary>
    public int RecommendedBatchSize { get; set; }
}

/// <summary>
/// Data template definition
/// </summary>
public class DataTemplate
{
    /// <summary>
    /// Template name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Field definitions
    /// </summary>
    public List<FieldDefinition> Fields { get; set; } = new();
}

/// <summary>
/// Field definition for data templates
/// </summary>
public class FieldDefinition
{
    /// <summary>
    /// Field name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Field data type
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Whether the field is required
    /// </summary>
    public bool Required { get; set; }

    /// <summary>
    /// Maximum field length (for string types)
    /// </summary>
    public int? MaxLength { get; set; }

    /// <summary>
    /// Field description
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Validation rules for import data
/// </summary>
public class ValidationRules
{
    /// <summary>
    /// General validation rules
    /// </summary>
    public List<string> General { get; set; } = new();

    /// <summary>
    /// Driver-specific validation rules
    /// </summary>
    public List<string> Driver { get; set; } = new();

    /// <summary>
    /// Vehicle-specific validation rules
    /// </summary>
    public List<string> Vehicle { get; set; } = new();
}
