# Build stage
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

# Copy project files
COPY ["FleetXQ.Tools.BulkImporter.WebApi/FleetXQ.Tools.BulkImporter.WebApi.csproj", "FleetXQ.Tools.BulkImporter.WebApi/"]
COPY ["FleetXQ.Tools.BulkImporter.Core/FleetXQ.Tools.BulkImporter.Core.csproj", "FleetXQ.Tools.BulkImporter.Core/"]

# Restore dependencies
RUN dotnet restore "FleetXQ.Tools.BulkImporter.WebApi/FleetXQ.Tools.BulkImporter.WebApi.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/FleetXQ.Tools.BulkImporter.WebApi"
RUN dotnet build "FleetXQ.Tools.BulkImporter.WebApi.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "FleetXQ.Tools.BulkImporter.WebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS final
WORKDIR /app

# Create non-root user
RUN groupadd -r fleetxq && useradd -r -g fleetxq fleetxq

# Create directories and set permissions
RUN mkdir -p /app/logs /app/data /app/temp && \
    chown -R fleetxq:fleetxq /app

# Copy published application
COPY --from=publish /app/publish .

# Switch to non-root user
USER fleetxq

# Expose port
EXPOSE 80
EXPOSE 443

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Set entry point
ENTRYPOINT ["dotnet", "FleetXQ.Tools.BulkImporter.WebApi.dll"]
