using System.ComponentModel.DataAnnotations;

namespace FleetXQ.Tools.BulkImporter.Core.Configuration;

/// <summary>
/// Configuration options for the bulk importer application
/// NOTE: This is a placeholder - the actual implementation will be moved from the console app
/// </summary>
public class BulkImporterOptions
{
    public const string SectionName = "BulkImporter";

    /// <summary>
    /// Default number of drivers to process when not specified
    /// </summary>
    [Range(1, 1000000)]
    public int DefaultDriversCount { get; set; } = 10000;

    /// <summary>
    /// Default number of vehicles to process when not specified
    /// </summary>
    [Range(1, 1000000)]
    public int DefaultVehiclesCount { get; set; } = 5000;

    /// <summary>
    /// Default batch size for bulk operations
    /// </summary>
    [Range(100, 100000)]
    public int DefaultBatchSize { get; set; } = 1000;

    /// <summary>
    /// Maximum allowed batch size
    /// </summary>
    [Range(1000, 100000)]
    public int MaxBatchSize { get; set; } = 50000;

    /// <summary>
    /// Timeout for SqlBulkCopy operations in seconds
    /// </summary>
    [Range(30, 3600)]
    public int BulkCopyTimeout { get; set; } = 300;

    /// <summary>
    /// Timeout for SQL commands in seconds
    /// </summary>
    [Range(30, 1800)]
    public int CommandTimeout { get; set; } = 120;

    /// <summary>
    /// Number of rows after which SqlBulkCopy sends a notification
    /// </summary>
    [Range(100, 10000)]
    public int NotifyAfter { get; set; } = 1000;

    /// <summary>
    /// Enable retry policy for transient failures
    /// </summary>
    public bool EnableRetry { get; set; } = true;

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    [Range(1, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts in seconds
    /// </summary>
    [Range(1, 60)]
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Enable validation of input data before processing
    /// </summary>
    public bool ValidationEnabled { get; set; } = true;

    /// <summary>
    /// Stop processing on first validation error
    /// </summary>
    public bool StopOnFirstError { get; set; } = false;

    /// <summary>
    /// Enable dealer-specific validation and scoping
    /// </summary>
    public bool DealerValidationEnabled { get; set; } = true;

    /// <summary>
    /// Require dealer selection before import operations
    /// </summary>
    public bool RequireDealerSelection { get; set; } = true;

    /// <summary>
    /// Default dealer ID to use when not specified (optional)
    /// </summary>
    public Guid? DefaultDealerId { get; set; }

    /// <summary>
    /// Default dealer name to use when not specified (optional)
    /// </summary>
    public string? DefaultDealerName { get; set; }

    /// <summary>
    /// Whether to clean up staging data after successful processing
    /// </summary>
    public bool CleanupStagingData { get; set; } = true;

    /// <summary>
    /// Use temporary tables instead of permanent staging tables
    /// </summary>
    public bool UseTempTables { get; set; } = true;

    /// <summary>
    /// Temporary table mode: SessionScoped or Global
    /// </summary>
    public string TempTableMode { get; set; } = "SessionScoped";

    /// <summary>
    /// Batch size for temporary table operations
    /// </summary>
    [Range(100, 50000)]
    public int TempTableBatchSize { get; set; } = 5000;

    /// <summary>
    /// Create indexes on temporary tables for performance
    /// </summary>
    public bool TempTableIndexes { get; set; } = true;

    /// <summary>
    /// Enable detailed logging of temporary table operations
    /// </summary>
    public bool LogTempTableOperations { get; set; } = true;
}

/// <summary>
/// Configuration options for data generation and processing
/// </summary>
public class DataGenerationOptions
{
    public const string SectionName = "DataGeneration";

    /// <summary>
    /// Directory for output files and reports
    /// </summary>
    [Required]
    public string OutputDirectory { get; set; } = "OutputFiles";

    /// <summary>
    /// Directory for archiving processed data
    /// </summary>
    [Required]
    public string ArchiveDirectory { get; set; } = "Archive";

    /// <summary>
    /// Directory for error reports
    /// </summary>
    [Required]
    public string ErrorDirectory { get; set; } = "Errors";

    /// <summary>
    /// Enable synthetic data generation for testing
    /// </summary>
    public bool EnableSyntheticDataGeneration { get; set; } = true;

    /// <summary>
    /// Base seed for random data generation (for reproducible results)
    /// </summary>
    public int RandomSeed { get; set; } = 42;

    /// <summary>
    /// Data generation batch size for memory management
    /// </summary>
    [Range(100, 50000)]
    public int GenerationBatchSize { get; set; } = 5000;

    /// <summary>
    /// Enable data validation before processing
    /// </summary>
    public bool ValidateGeneratedData { get; set; } = true;

    /// <summary>
    /// Maximum memory usage for data generation in MB
    /// </summary>
    [Range(100, 10000)]
    public int MaxMemoryUsageMB { get; set; } = 1000;
}

/// <summary>
/// Database connection configuration
/// </summary>
public class ConnectionStringOptions
{
    public const string SectionName = "ConnectionStrings";

    /// <summary>
    /// Default database connection string
    /// </summary>
    [Required]
    public string DefaultConnection { get; set; } = string.Empty;

    /// <summary>
    /// FleetXQ database connection string
    /// </summary>
    [Required]
    public string FleetXQConnection { get; set; } = string.Empty;
}

/// <summary>
/// Environment-specific configuration options
/// </summary>
public class EnvironmentOptions
{
    public const string SectionName = "Environment";

    /// <summary>
    /// Environment name (Development, Staging, Pilot, Production)
    /// </summary>
    [Required]
    public string Name { get; set; } = "Development";

    /// <summary>
    /// Environment description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Whether operations require approval in this environment
    /// </summary>
    public bool RequiresApproval { get; set; } = false;

    /// <summary>
    /// Maximum operation size allowed in this environment
    /// </summary>
    [Range(1, 1000000)]
    public int MaxOperationSize { get; set; } = 100000;

    /// <summary>
    /// Webhook URLs for notifications
    /// </summary>
    public List<string> NotificationWebhooks { get; set; } = new();

    /// <summary>
    /// Maintenance windows when operations should be restricted
    /// </summary>
    public List<MaintenanceWindow> MaintenanceWindows { get; set; } = new();
}

/// <summary>
/// Maintenance window configuration
/// </summary>
public class MaintenanceWindow
{
    /// <summary>
    /// Start time (HH:mm format)
    /// </summary>
    [Required]
    public string Start { get; set; } = string.Empty;

    /// <summary>
    /// End time (HH:mm format)
    /// </summary>
    [Required]
    public string End { get; set; } = string.Empty;

    /// <summary>
    /// Time zone for the maintenance window
    /// </summary>
    public string TimeZone { get; set; } = "UTC";

    /// <summary>
    /// Description of the maintenance window
    /// </summary>
    public string Description { get; set; } = string.Empty;
}
