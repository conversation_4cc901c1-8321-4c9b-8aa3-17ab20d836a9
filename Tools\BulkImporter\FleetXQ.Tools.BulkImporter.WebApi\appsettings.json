{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ;Trusted_Connection=true;MultipleActiveResultSets=true;", "FleetXQConnection": "Server=(localdb)\\mssqllocaldb;Database=FleetXQ;Trusted_Connection=true;MultipleActiveResultSets=true;"}, "BulkImporter": {"DefaultDriversCount": 10000, "DefaultVehiclesCount": 5000, "DefaultBatchSize": 1000, "MaxBatchSize": 50000, "BulkCopyTimeout": 300, "CommandTimeout": 120, "NotifyAfter": 1000, "EnableRetry": true, "MaxRetryAttempts": 3, "RetryDelaySeconds": 5, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": true, "DefaultDealerId": null, "DefaultDealerName": null, "UseTempTables": true, "TempTableMode": "SessionScoped", "TempTableBatchSize": 5000, "TempTableIndexes": true, "LogTempTableOperations": true}, "DataGeneration": {"OutputDirectory": "OutputFiles", "ArchiveDirectory": "Archive", "ErrorDirectory": "Errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 42, "GenerationBatchSize": 5000, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 1000}, "Environment": {"Name": "Development", "Description": "Local development environment", "RequiresApproval": false, "MaxOperationSize": 50000, "NotificationWebhooks": []}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:8080"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}, "RateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*/api/bulk-import/*", "Period": "1m", "Limit": 10}]}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] [{Environment}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/bulkimporter-webapi-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{Environment}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName"], "Properties": {"Application": "FleetXQ.BulkImporter.WebApi", "Environment": "Development"}}, "AllowedHosts": "*"}