{"ConnectionStrings": {"DefaultConnection": "Server=pilot-db-server;Database=FleetXQ_Pilot;User Id=bulkimporter_app;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;", "FleetXQConnection": "Server=pilot-db-server;Database=FleetXQ_Pilot;User Id=bulkimporter_app;Password=***********;MultipleActiveResultSets=true;Encrypt=true;TrustServerCertificate=false;"}, "BulkImporter": {"DefaultDriversCount": 5000, "DefaultVehiclesCount": 2500, "DefaultBatchSize": 500, "MaxBatchSize": 10000, "BulkCopyTimeout": 600, "CommandTimeout": 300, "ValidationEnabled": true, "StopOnFirstError": false, "DealerValidationEnabled": true, "RequireDealerSelection": true, "UseTempTables": true, "TempTableMode": "SessionScoped", "TempTableBatchSize": 2000, "LogTempTableOperations": true}, "DataGeneration": {"OutputDirectory": "/app/data/output", "ArchiveDirectory": "/app/data/archive", "ErrorDirectory": "/app/data/errors", "EnableSyntheticDataGeneration": true, "RandomSeed": 42, "GenerationBatchSize": 2000, "ValidateGeneratedData": true, "MaxMemoryUsageMB": 1000}, "Environment": {"Name": "Pilot", "Description": "Pilot environment for customer testing", "RequiresApproval": true, "MaxOperationSize": 10000, "NotificationWebhooks": ["https://hooks.slack.com/services/PILOT_WEBHOOK_URL"], "MaintenanceWindows": [{"Description": "Weekly maintenance window", "Start": "01:00", "End": "03:00", "TimeZone": "UTC"}]}, "Cors": {"AllowedOrigins": ["https://pilot-bulkimporter.fleetxq.com", "https://pilot.fleetxq.com"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Authorization", "Content-Type", "X-Requested-With"], "AllowCredentials": true}, "RateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 150}, {"Endpoint": "*/api/bulk-import/*", "Period": "1m", "Limit": 15}]}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] [{Environment}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/bulkimporter-webapi-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] [{Environment}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Properties": {"Application": "FleetXQ.BulkImporter.WebApi", "Environment": "Pilot"}}}