import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Environment } from '@/types/environment'

export const useEnvironmentStore = defineStore('environment', () => {
  // State
  const environments = ref<Environment[]>([])
  const currentEnvironment = ref<Environment | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isProduction = computed(() => 
    currentEnvironment.value?.name.toLowerCase() === 'production'
  )
  
  const requiresApproval = computed(() => 
    currentEnvironment.value?.requiresApproval || false
  )
  
  const maxOperationSize = computed(() => 
    currentEnvironment.value?.maxOperationSize || 50000
  )

  const isInMaintenanceWindow = computed(() => {
    if (!currentEnvironment.value?.maintenanceWindows?.length) {
      return false
    }
    
    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()
    
    return currentEnvironment.value.maintenanceWindows.some(window => {
      const [startHour, startMin] = window.start.split(':').map(Number)
      const [endHour, endMin] = window.end.split(':').map(Number)
      const startTime = startHour * 60 + startMin
      const endTime = endHour * 60 + endMin
      
      return currentTime >= startTime && currentTime <= endTime
    })
  })

  // Actions
  const fetchEnvironments = async () => {
    loading.value = true
    error.value = null
    
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/environments')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      environments.value = await response.json()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch environments'
      console.error('Error fetching environments:', err)
    } finally {
      loading.value = false
    }
  }

  const setCurrentEnvironment = (environment: Environment) => {
    currentEnvironment.value = environment
    localStorage.setItem('selectedEnvironment', JSON.stringify(environment))
  }

  const loadSavedEnvironment = () => {
    const saved = localStorage.getItem('selectedEnvironment')
    if (saved) {
      try {
        currentEnvironment.value = JSON.parse(saved)
      } catch (err) {
        console.error('Error loading saved environment:', err)
        localStorage.removeItem('selectedEnvironment')
      }
    }
  }

  const clearEnvironment = () => {
    currentEnvironment.value = null
    localStorage.removeItem('selectedEnvironment')
  }

  return {
    // State
    environments,
    currentEnvironment,
    loading,
    error,
    
    // Getters
    isProduction,
    requiresApproval,
    maxOperationSize,
    isInMaintenanceWindow,
    
    // Actions
    fetchEnvironments,
    setCurrentEnvironment,
    loadSavedEnvironment,
    clearEnvironment
  }
})
